[build-system]
requires = ["setuptools>=61.0", "wheel", ]
build-backend = "setuptools.build_meta"

[project]
name = "blitzy-flask-utils"
version = "0.0.242"
description = "Flask util to expose common utils for flask."
readme = "README.md"
classifiers = ["Programming Language :: Python :: 3", "License :: Apache 2.0 License", "Operating System :: OS Independent", ]
dependencies = ["Flask==3.1.1", "Flask-Cors==6.0.0", "pydantic[email]>=2.9.2", "sqlalchemy>=1.4", ]
requires-python = ">=3.8"
[[project.authors]]
name = "Chaitanya Baraskar"

[project.license]
text = "Apache 2.0"

[project.urls]
Homepage = "https://blitzy.ai"
Repository = "https://github.com/blitzy-ai/python-utils"
