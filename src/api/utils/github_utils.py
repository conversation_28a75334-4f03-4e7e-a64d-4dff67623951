from typing import Any, Dict, List, Optional, Union

import requests
from blitzy_utils.common import blitzy_exponential_retry
from blitzy_utils.github import get_github_installations
from blitzy_utils.logger import logger
from common_models.models import GithubInstallation, VersionControlSystem
from requests import ConnectTimeout, RequestException
from urllib3.exceptions import ConnectTimeoutError, MaxRetryError

from github import (BadCredentialsException, Github, GithubException,
                    GithubIntegration, RateLimitExceededException)
from src.api.routes.secret_manager import get_github_secret_value
from src.azure.azure_app_service import AzureAppService
from src.consts import (GITHUB_APP_ID, GITHUB_CREATE_REPO_DESCRIPTION,
                        GITHUB_PRIVATE_KEY)
from src.error.errors import (GithubBaseError, GithubPermissionError,
                              GithubRepoAlreadyExistsError)
from src.service.azure_service import fetch_azure_secret

# TODO: For below two functions, we should come up with or implement a better way
#  to standardize retry logic throughout services.


def get_orgs_by_installations(installation_list: List[GithubInstallation]) -> List[Dict[str, Any]]:
    org_list = []

    for installation in installation_list:
        # Reset retry state for each installation
        retry_state = {"retries": 0, "max": 3}
        orgs = get_installation_org(installation.installation_id, installation.svc_type, retry_state)

        # Handle different return types
        if orgs is None:
            continue
        elif isinstance(orgs, list):
            for org in orgs:
                # azure devops we get list of azure devops organizations IDs
                # but we also need to have Azure(without devops) organization ID
                for azure_devops_org in org.values():
                    azure_devops_org['orgId'] = installation.target_id
            org_list.extend(orgs)
        else:
            for github_org in orgs.values():
                github_org['orgId'] = installation.target_id
            orgs['orgId'] = installation.target_id
            org_list.append(orgs)

    return org_list


def get_installation_org(installation_id: str, svc_type: str,
                         retry_state: Dict[str, int]) -> Union[Dict[str, Any], List[Dict[str, Any]], None]:
    while retry_state["retries"] < retry_state["max"]:
        try:

            if svc_type == VersionControlSystem.GITHUB:
                git_integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)
                installation = git_integration.get_app_installation(installation_id)

                account = installation.account
                return {
                    account.login: {
                        "name": account.login,
                        "id": str(account.id),
                        "type": "User",
                        "installationId": installation_id,
                    }
                }

            elif svc_type == VersionControlSystem.AZURE_DEVOPS:
                instance = AzureAppService()
                secret = fetch_azure_secret(installation_id)

                org_details = instance.get_azure_devops_org_details(
                    access_token=secret.accessToken,
                    installation_id=installation_id
                )

                logger.info(f"Org Details: {org_details}")
                logger.info(f"Org Details type: {type(org_details)}")

                # Azure returns a dict where keys are org names and values are org details
                # Convert this to a list of individual org dictionaries
                normalized_orgs = []

                for org_name, org_data in org_details.items():
                    # Each org_data already has the correct structure
                    normalized_org = {
                        org_name: org_data
                    }
                    normalized_orgs.append(normalized_org)

                logger.info(f"Normalized orgs count: {len(normalized_orgs)}")
                return normalized_orgs

        except (ConnectTimeout, ConnectTimeoutError, MaxRetryError, RequestException) as e:
            logger.warning(f"Retryable error: {e}")
            retry_state["retries"] += 1
            if retry_state["retries"] >= retry_state["max"]:
                raise GithubBaseError(f"Retry limit reached for installation {installation_id}: {str(e)}")

        except RateLimitExceededException:
            logger.warning("API rate limit exceeded")
            raise GithubBaseError("API rate limit exceeded")

        except BadCredentialsException as e:
            logger.warning(f"Authentication failed: {str(e)}")
            raise GithubBaseError("Authentication failed")

        except GithubException as e:
            error_message = f"GitHub API error (status {e.status}): {str(e)}"
            logger.warning(error_message)
            raise GithubBaseError(error_message)

        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            raise Exception(f"Unexpected error occurred: {str(e)}")

    return None


def _get_github_repos(installation: GithubInstallation) -> Dict[str, Any]:
    """Get GitHub repositories for a single installation."""
    logger.debug(f"Fetching GitHub repositories for installation {installation.installation_id}")

    try:
        github_repos = get_repos_by_installation_id(installation.installation_id)
        if github_repos:
            logger.info(f"Successfully fetched GitHub repositories: {len(github_repos.get('repositories', {}))} repos")
        else:
            logger.warning(f"No repositories returned for GitHub installation {installation.installation_id}")
        return github_repos
    except Exception as e:
        logger.error(f"Failed to fetch GitHub repositories for installation {installation.installation_id}: {str(e)}")
        raise


def _get_azure_repos(installation: GithubInstallation) -> Dict[str, Any]:
    """Get Azure DevOps repositories for a single installation."""
    logger.debug(f"Fetching Azure repositories for installation {installation.installation_id}")

    try:
        logger.debug(f"Fetching Azure secret for installation ID: {installation.installation_id}")
        secret = fetch_azure_secret(installation.installation_id)

        instance = AzureAppService()
        azure_repos = instance.get_repos_by_access_token(secret.accessToken)

        if azure_repos:
            logger.info(f"Successfully fetched Azure repositories: {len(azure_repos.get('repositories', {}))} repos")
        else:
            logger.warning(f"No repositories returned for Azure installation {installation.installation_id}")

        return azure_repos
    except Exception as e:
        logger.error(f"Failed to fetch Azure repositories for installation {installation.installation_id}: {str(e)}")
        raise


def get_repos_by_installation(installation: GithubInstallation) -> Dict[str, Any]:
    """
    Get repositories from a single GitHub or Azure DevOps installation.

    Args:
        installation: Single installation (GitHub or Azure)

    Returns:
        Dictionary containing repository information
    """
    logger.info(f"Getting repositories for installation type: {installation.svc_type}")

    if installation.svc_type == VersionControlSystem.GITHUB:
        logger.debug("Processing GitHub installation")
        return [_get_github_repos(installation)]
    elif installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        logger.debug("Processing Azure DevOps installation")
        return [_get_azure_repos(installation)]
    else:
        logger.error(f"Unsupported installation type: {installation.svc_type}")
        raise ValueError(f"Unsupported installation type: {installation.svc_type}")


@blitzy_exponential_retry()
def get_repos_by_installation_id(installation_id) -> Optional[Dict[str, Any]]:
    """
    Fetches repositories accessible through a GitHub App installation ID using direct API calls.

    This function returns only repositories that the specific installation has access to,
    handling pagination to ensure all repositories are retrieved.

    :param installation_id: The GitHub App installation ID to use.
    :return: A dictionary containing repository information, or None if an error occurs.
    """
    try:
        if not installation_id:
            logger.error("No installation ID provided")
            raise Exception("No installation ID provided")

        git_integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)
        access_token = git_integration.get_access_token(int(installation_id))
        headers = {
            "Authorization": f"Bearer {access_token.token}",
            "Accept": "application/vnd.github.v3+json"
        }

        all_repos = []
        next_url = "https://api.github.com/installation/repositories"

        while next_url:
            response = requests.get(next_url, headers=headers)

            if response.status_code != 200:
                logger.error(f"Failed to get installation repositories: {response.status_code}, {response.text}")
                raise Exception(f"Failed to get installation repositories: {response.status_code}")

            repo_data = response.json()
            all_repos.extend(repo_data.get("repositories", []))

            next_url = None
            if 'Link' in response.headers:
                links = requests.utils.parse_header_links(response.headers['Link'].rstrip('>').replace('>,<', ',<'))
                for link in links:
                    if link['rel'] == 'next':
                        next_url = link['url']
                        break

        repos = {}
        repos_by_id = {}

        for repo in all_repos:
            repo_info = {
                "id": str(repo["id"]),
                "name": repo["name"],
                "fullName": repo["full_name"],
                "private": repo["private"],
                "installationId": str(installation_id),
            }

            repos[repo["name"]] = repo_info
            repos_by_id[str(repo["id"])] = repo_info

        return {
            "repositories": repos,
            "repositoriesById": repos_by_id,
        }

    except Exception as e:
        logger.warning(f"Error fetching repositories by installation ID: {str(e)}")
        return None


@blitzy_exponential_retry()
def get_branches_by_repo_id(installation_id: str, repo_id: str) -> Optional[Dict[str, Any]]:
    """
    Fetches branches for the given repository ID within the context of a specific GitHub installation.

    This function interacts with the GitHub API to retrieve and format a list of branches from a given
    repository. It uses the GitHub App credentials to authenticate via the respective installation ID.
    The branches are returned alongside basic repository information in a structured format.

    :param installation_id: The installation ID of the GitHub App used for authentication with a
        specific repository.
    :param repo_id: The unique identifier of the GitHub repository for which branches are fetched.
    :return: A dictionary containing the repository information (ID, name, full name) and a list of
        branches, where each branch has its name and protection status. If any error occurs, returns
        None.
    """
    try:
        git_integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)
        access_token = git_integration.get_access_token(installation_id)

        g = Github(access_token.token)
        repo = g.get_repo(int(repo_id))
        branches = []
        branch_by_name_dict = {}
        for branch in repo.get_branches():
            branches.append({
                "name": branch.name,
                "protected": branch.protected,
            })
            branch_by_name_dict.update({
                branch.name: {
                    "name": branch.name,
                    "protected": branch.protected,
                    "installationId": installation_id,
                }})

        return {
            "repository": {
                "id": str(repo.id),
                "name": repo.name,
                "fullName": repo.full_name
            },
            "branches": branches,
            "branchesByName": branch_by_name_dict,
        }

    except Exception as e:
        logger.warning(f"Error fetching branches: {str(e)}")
        return None


@blitzy_exponential_retry()
def get_repo_by_id(installation_id: str, repo_id: str) -> Optional[Dict[str, Any]]:
    try:
        git_integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)
        access_token = git_integration.get_access_token(installation_id)

        g = Github(access_token.token)
        repo = g.get_repo(int(repo_id))
        return {
            "id": str(repo.id),
            "name": repo.name,
            "fullName": repo.full_name,
            "installationId": installation_id,
            "private": repo.private,
        }

    except Exception as e:
        logger.warning(f"Error fetching branches: {str(e)}")
        return None


@blitzy_exponential_retry()
def get_branch_head_commit(installation_id, repo_id, branch_name):
    """
    Fetches information about the head commit of a specific branch in a repository for a given GitHub installation.
    Uses a GitHub App integration to authenticate and interact with the GitHub API. The function takes a repository
    identifier and branch name, retrieves the branch, and provides details about the branch"s latest commit such as
    the SHA, URL, commit message, author, and date.

    :param installation_id: The identifier of the GitHub App installation used to authenticate API requests.
    :param repo_id: The unique identifier for the repository. It can be an integer ID or a string representing the ID.
    :param branch_name: The name of the branch whose head commit information needs to be fetched.
    :return: A dictionary with branch and head commit information, or None if an error occurs. Includes the branch name,
             commit SHA, commit message, commit URL, author name, and commit date.
    """
    try:
        git_integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)
        access_token = git_integration.get_access_token(installation_id)

        g = Github(access_token.token)
        repo = g.get_repo(int(repo_id) if isinstance(repo_id, str) else repo_id)
        branch = repo.get_branch(branch_name)
        return {
            "name": branch.name,
            "commit": {
                "sha": branch.commit.sha,
                "url": branch.commit.html_url,
                "message": branch.commit.commit.message,
                "author": branch.commit.commit.author.name,
                "date": branch.commit.commit.author.date.isoformat()
            }
        }

    except Exception as e:
        logger.warning(f"Error fetching branch head commit: {str(e)}")
        return None


@blitzy_exponential_retry()
def manage_pull_request(installation_id: str, repo_id: str, pr_number: int, action: str, merge_method: str = "merge"):
    """
    Manages a pull request in a specified GitHub repository. The method
    allows actions such as merging or closing a pull request. When merging,
    it checks if the pull request can be merged and attempts to do so using
    the specified merge method.

    Uses GitHub App credentials to authenticate and perform operations on
    a repository. Actions are performed based on the input parameters
    defining the operation and target pull request.

    :param installation_id: ID of the GitHub installation to authenticate with.
    :param repo_id: ID of the repository where the pull request exists.
    :param pr_number: Number of the pull request to manage.
    :param action: Specifies the action to perform on the pull request.
      Supported actions are "merge" or "close".
    :param merge_method: The method to use for merging the pull request.
      Required if action is "merge". Possible values are "merge", "squash", or "rebase".
    :return: A dictionary containing the success status of the operation, an explanatory
      message, and optionally additional details (e.g., merged status, SHA of the merge).
    :rtype: dict
    """
    try:
        # Create GitHub Integration instance
        git_integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)

        # Get installation token
        access_token = git_integration.get_access_token(int(installation_id))

        # Create GitHub instance with installation token
        g = Github(access_token.token)

        # Get repository
        repo = g.get_repo(int(repo_id) if isinstance(repo_id, str) else repo_id)

        # Get pull request
        pr = repo.get_pull(pr_number)

        # Take action based on requested operation
        if action.lower() == "merge":
            # Check if PR can be merged
            if not pr.mergeable:
                return {
                    "success": False,
                    "message": "Pull request cannot be merged. There may be conflicts."
                }

            # Merge the PR with specified method
            result = pr.merge(
                commit_title=f"Merge pull request #{pr_number}",
                commit_message="Merged via API",
                merge_method=merge_method
            )

            return {
                "success": True,
                "message": "Pull request merged successfully",
                "merged": result.merged,
                "sha": result.sha
            }

        elif action.lower() == "close":
            # Close the PR without merging
            pr.edit(state="closed")

            return {
                "success": True,
                "message": "Pull request closed without merging"
            }

        else:
            return {
                "success": False,
                "message": f"Invalid action '{action}'. Use 'merge" or "close'."
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"Error managing pull request: {str(e)}"
        }


@blitzy_exponential_retry()
def get_default_branch(installation_id, owner_name, repo_name):
    """
    Fetches the default branch of a specific repository using GitHub App installation.
    Works with both organization and user repositories.

    :param installation_id: The GitHub App installation ID
    :param owner_name: The organization or user name that owns the repository
    :param repo_name: The name of the repository
    :return: The default branch name (e.g., 'main' or 'master')
    """
    try:
        git_integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)
        access_token = git_integration.get_access_token(installation_id)
        g = Github(access_token.token)

        full_repo_name = f"{owner_name}/{repo_name}"
        repo = g.get_repo(full_repo_name)
        return repo.default_branch

    except GithubException as e:
        logger.warning(f"GitHub API Error: {e.status} - {e.data.get('message')}")
        return None
    except Exception as e:
        logger.error(f"Error fetching default branch: {str(e)}")
        return None


@blitzy_exponential_retry()
def get_installation_id_for_account(installation_ids, account_name):
    """
    Find the installation ID that has access to the specified account name.

    Args:
        app_id (int): The GitHub App ID
        private_key (str): The private key content as a string
        installation_ids (list): A list of installation IDs to check
        account_name (str): The name of the GitHub account to find

    Returns:
        int or None: The installation ID if the account is found, otherwise None
    """
    integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)

    for installation_id in installation_ids:
        try:
            installation = integration.get_app_installation(installation_id)
            if installation.account.login.lower() == account_name.lower():
                logger.info(
                    f"Found matching installation {installation_id} for account"
                    f" '{account_name}' (Type: {installation.account.type})")
                return installation_id
            else:
                logger.info(
                    f"Installation {installation_id} belongs to '{installation.account.login}'"
                    f" (Type: {installation.account.type}), not '{account_name}'")

        except Exception as e:
            logger.warning(f"Failed to check installation {installation_id}: {str(e)}")
            continue

    logger.warning(f"No installation found for account '{account_name}' in the provided installation IDs")
    return None


@blitzy_exponential_retry()
def create_github_repository(
    installation_id: str, repo_name: str, private: bool = True
) -> Dict[str, Any]:
    """
    Creates a new GitHub repository using direct access token authentication.
    Handles both user and organization account types. Automatically initializes the repository
    with a README file if it does not exist.

    :param installation_id: GitHub App installation ID to retrieve access token.
    :param repo_name: Name of the repository to be created.
    :param private: Specifies whether the repository is private. Defaults to True.
    :return: Dictionary containing the created repository information.
    :raises ValueError: If the repository already exists.
    :raises GithubPermissionError: If the app lacks necessary permissions.
    :raises GithubRepoAlreadyExistsError: If the repository already exists.
    :raises Exception: For other errors encountered during repository creation.
    """
    try:
        # Get user access token using your existing function
        response = get_github_secret_value(installation_id, "latest")
        access_token = response.get("access_token")
        installation_id = response.get("installation_id")
        if not access_token:
            logger.error("Failed to get GitHub access token")
            raise Exception("Failed to get GitHub access token")

        # Create GitHub instance with user token
        g = Github(access_token)

        # Get installations to determine account type
        installations = get_github_installations(access_token=access_token)

        login_name = ''
        target_type = 'User'  # Default to User type

        # Find the installation that matches our installation_id
        for installation in installations:
            if str(installation['id']) == installation_id:
                login_name = installation['account']['login']
                target_type = installation['account']['type']
                break

        if not login_name:
            logger.error(f"Could not find installation with ID {installation_id}")
            raise Exception(f"Invalid installation ID: {installation_id}")

        # Set up repository options
        repo_options = {
            "name": repo_name,
            "private": private,
            "description": GITHUB_CREATE_REPO_DESCRIPTION,
            "has_issues": True,
            "has_projects": True,
            "has_wiki": True,
            "auto_init": True  # Initialize with README
        }

        # Initialize repo variable
        repo = None

        # Check if repository exists and create based on account type
        if target_type == "Organization":
            # Organization account
            org = g.get_organization(login_name)

            try:
                org.get_repo(repo_name)  # Will raise 404 if not found
                raise ValueError(f"Repository '{login_name}/{repo_name}' already exists")
            except GithubException as e:
                if e.status != 404:
                    raise
                # Repository doesn't exist, create it
                logger.info(f"Repository '{login_name}/{repo_name}' does not exist, creating it")
                repo = org.create_repo(**repo_options)
        else:
            # User account
            user = g.get_user()

            try:
                user.get_repo(repo_name)  # Will raise 404 if not found
                raise ValueError(f"Repository '{login_name}/{repo_name}' already exists")
            except GithubException as e:
                if e.status == 403:
                    error_msg = (f"Github App lacks necessary permission for {repo_name}."
                                 f" Please update app configuration")
                    logger.error(error_msg)
                    raise GithubPermissionError(error_msg)
                elif e.status != 404:
                    raise
                # Repository doesn't exist, create it
                logger.info(f"Creating new repository: {repo_name} for user {login_name}")
                repo = user.create_repo(**repo_options)

        # Ensure repo was created successfully
        if repo is None:
            raise Exception("Failed to create repository - repo object is None")

        # Return repository information
        return repo

    except ValueError as e:
        # Re-raise ValueError for already existing repositories
        logger.warning(f"Failed to create repository with error {str(e)}:")
        raise GithubRepoAlreadyExistsError(str(e))

    except GithubPermissionError as e:
        # Re-raise permission errors
        logger.error(f"Permission error: {str(e)}")
        raise

    except GithubException as e:
        error_msg = f"GitHub error creating repository: {e.status} - {e.data.get('message', str(e))}"
        logger.error(error_msg)
        raise GithubException(e.status, error_msg) from e

    except Exception as e:
        logger.error(f"Error creating repository: {str(e)}")
        raise
